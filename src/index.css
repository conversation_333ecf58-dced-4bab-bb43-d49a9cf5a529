@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables */
:root {
  --brand-primary: #3b82f6;
  --brand-secondary: #8b5cf6;
  --brand-accent: #06b6d4;
  --brand-gradient: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4);
}

/* Custom Components */
@layer components {
  .brand-button {
    @apply bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 hover:from-blue-700 hover:via-purple-700 hover:to-cyan-700;
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  .brand-card {
    @apply bg-gray-900/50 backdrop-blur-sm border border-gray-800/50;
    background: linear-gradient(135deg, rgba(17, 24, 39, 0.8), rgba(31, 41, 55, 0.6));
    border: 1px solid rgba(75, 85, 99, 0.3);
  }

  .brand-accent {
    @apply bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent;
  }
}

/* Custom Animations */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.floating-animation {
  animation: floating 3s ease-in-out infinite;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-900;
}

::-webkit-scrollbar-thumb {
  @apply bg-gradient-to-b from-blue-600 to-purple-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply from-blue-500 to-purple-500;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}